{"bot": {"token": "MTQxNjc2ODM2NTMwODkzNjI4Mw.GFCoKN.Pnbjr-X07QkZ7CHBotrJY3iR0P33GynDjZlJ8Q", "command_prefix": "!", "description": "Discord Inventory Management Bot"}, "database": {"path": "inventory.db", "cleanup_interval_minutes": 30}, "permissions": {"shop_keeper_role": "1395229769016148038", "allow_manage_guild": true, "allow_administrator": true}, "inventory": {"max_items_per_user": 50, "max_description_length": 500, "required_image_size": [128, 128], "max_expiration_days": 365, "min_expiration_minutes": 1}, "ui": {"embed_colors": {"success": "#00ff00", "error": "#ff0000", "warning": "#ffaa00", "info": "#0099ff", "pending": "#ff6600"}, "timeouts": {"button_interaction_seconds": 300, "input_wait_seconds": 120, "view_timeout_seconds": 300}}, "features": {"auto_cleanup_enabled": true, "image_validation_enabled": true, "permission_checks_enabled": true, "logging_enabled": true}, "logging": {"level": "INFO", "file": "bot.log", "max_file_size_mb": 10, "backup_count": 3}}