import discord
from discord.ext import commands, tasks
from discord import app_commands
import aiosqlite
import asyncio
import json
import os
import io
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image
from logging.handlers import RotatingFileHandler

# ================================
# CONFIGURATION SECTION
# ================================
# Edit these values for your bot setup

CONFIG = {
    "bot": {
        "token": "MTQxNjc2ODM2NTMwODkzNjI4Mw.GFCoKN.Pnbjr-X07QkZ7CHBotrJY3iR0P33GynDjZlJ8Q",  # Replace with your Discord bot token
        "command_prefix": "!",
        "description": "Discord Inventory Management Bot"
    },
    "database": {
        "path": "inventory.db",
        "cleanup_interval_minutes": 30
    },
    "permissions": {
        "shop_keeper_role": "1395229769016148038",  # IMPORTANT: Use exact role ID or role name
        "allow_manage_guild": True,  # Allow users with "Manage Server" permission
        "allow_administrator": True  # Allow administrators
    },
    "inventory": {
        "max_items_per_user": 50,
        "max_description_length": 500,
        "required_image_size": [1152, 768],
        "max_expiration_days": 365,
        "min_expiration_minutes": 1
    },
    "ui": {
        "embed_colors": {
            "success": "#00ff00",
            "error": "#ff0000",
            "warning": "#ffaa00", 
            "info": "#0099ff",
            "pending": "#ff6600"
        },
        "timeouts": {
            "button_interaction_seconds": 300,
            "input_wait_seconds": 120,
            "view_timeout_seconds": 300
        }
    },
    "features": {
        "auto_cleanup_enabled": True,
        "image_validation_enabled": True,
        "permission_checks_enabled": True,
        "logging_enabled": True
    },
    "logging": {
        "level": "INFO",
        "file": "bot.log",
        "max_file_size_mb": 10,
        "backup_count": 3
    }
}

# ================================
# CONFIGURATION HELPER CLASS
# ================================

class BotConfig:
    """Configuration helper class."""
    
    def __init__(self, config_dict):
        self._config = config_dict
    
    def get(self, key_path: str, default: Any = None):
        """Get a configuration value by key path (e.g., 'bot.token')."""
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    # Bot configuration properties
    @property
    def token(self) -> str:
        return os.getenv('DISCORD_TOKEN') or self.get('bot.token')
    
    @property
    def command_prefix(self) -> str:
        return self.get('bot.command_prefix', '!!!')
    
    @property
    def bot_description(self) -> str:
        return self.get('bot.description', 'Discord Inventory Management Bot')
    
    @property
    def database_path(self) -> str:
        return os.getenv('DATABASE_PATH') or self.get('database.path', 'inventory.db')
    
    @property
    def cleanup_interval_minutes(self) -> int:
        return int(os.getenv('CLEANUP_INTERVAL_MINUTES', self.get('database.cleanup_interval_minutes', 30)))
    
    @property
    def shop_keeper_role(self) -> str:
        return os.getenv('SHOP_KEEPER_ROLE') or self.get('permissions.shop_keeper_role', 'Shop Keeper')
    
    @property
    def allow_manage_guild(self) -> bool:
        return self.get('permissions.allow_manage_guild', True)
    
    @property
    def allow_administrator(self) -> bool:
        return self.get('permissions.allow_administrator', True)
    
    @property
    def max_items_per_user(self) -> int:
        return self.get('inventory.max_items_per_user', 50)
    
    @property
    def max_description_length(self) -> int:
        return self.get('inventory.max_description_length', 500)
    
    @property
    def required_image_size(self) -> Tuple[int, int]:
        size = self.get('inventory.required_image_size', [128, 128])
        return tuple(size) if isinstance(size, list) and len(size) == 2 else (128, 128)
    
    @property
    def max_expiration_days(self) -> int:
        return self.get('inventory.max_expiration_days', 365)
    
    @property
    def min_expiration_minutes(self) -> int:
        return self.get('inventory.min_expiration_minutes', 1)
    
    @property
    def embed_colors(self) -> Dict[str, str]:
        return self.get('ui.embed_colors', {
            'success': '#00ff00',
            'error': '#ff0000',
            'warning': '#ffaa00',
            'info': '#0099ff',
            'pending': '#ff6600'
        })
    
    @property
    def input_wait_timeout(self) -> int:
        return self.get('ui.timeouts.input_wait_seconds', 120)
    
    @property
    def view_timeout(self) -> int:
        return self.get('ui.timeouts.view_timeout_seconds', 300)
    
    @property
    def auto_cleanup_enabled(self) -> bool:
        return self.get('features.auto_cleanup_enabled', True)
    
    @property
    def image_validation_enabled(self) -> bool:
        return self.get('features.image_validation_enabled', True)
    
    @property
    def permission_checks_enabled(self) -> bool:
        return self.get('features.permission_checks_enabled', True)
    
    @property
    def logging_enabled(self) -> bool:
        return self.get('features.logging_enabled', True)
    
    @property
    def log_level(self) -> str:
        return self.get('logging.level', 'INFO')
    
    @property
    def log_file(self) -> str:
        return self.get('logging.file', 'bot.log')
    
    @property
    def log_max_file_size_mb(self) -> int:
        return self.get('logging.max_file_size_mb', 10)
    
    @property
    def log_backup_count(self) -> int:
        return self.get('logging.backup_count', 3)

# Initialize config
config = BotConfig(CONFIG)

# ================================
# DATABASE CLASS
# ================================

class InventoryDatabase:
    """Database handler for inventory items."""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or config.database_path
    
    async def initialize(self):
        """Initialize the database and create tables if they don't exist."""
        async with aiosqlite.connect(self.db_path) as db:
            # Create table with new schema
            await db.execute("""
                CREATE TABLE IF NOT EXISTS inventory_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    name TEXT NOT NULL DEFAULT 'Unnamed Item',
                    description TEXT NOT NULL,
                    image_url TEXT,
                    expiration_time TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Check if name column exists, if not add it (for existing databases)
            cursor = await db.execute("PRAGMA table_info(inventory_items)")
            columns = await cursor.fetchall()
            column_names = [column[1] for column in columns]

            if 'name' not in column_names:
                await db.execute("ALTER TABLE inventory_items ADD COLUMN name TEXT NOT NULL DEFAULT 'Unnamed Item'")

            await db.commit()
    
    async def add_item(self, user_id: int, name: str, description: str, image_url: str, expiration_minutes: int) -> int:
        """Add a new item to the inventory."""
        expiration_time = datetime.now() + timedelta(minutes=expiration_minutes)

        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                INSERT INTO inventory_items (user_id, name, description, image_url, expiration_time)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, name, description, image_url, expiration_time))
            await db.commit()
            return cursor.lastrowid
    
    async def get_user_inventory(self, user_id: int) -> List[Dict]:
        """Get all non-expired items for a user."""
        current_time = datetime.now()
        
        async with aiosqlite.connect(self.db_path) as db:
            # First, clean up expired items
            await db.execute("""
                DELETE FROM inventory_items 
                WHERE expiration_time < ?
            """, (current_time,))
            await db.commit()
            
            # Then get the remaining items (limit to max items per user)
            cursor = await db.execute("""
                SELECT id, name, description, image_url, expiration_time, created_at
                FROM inventory_items
                WHERE user_id = ? AND expiration_time > ?
                ORDER BY created_at DESC
                LIMIT ?
            """, (user_id, current_time, config.max_items_per_user))

            rows = await cursor.fetchall()
            return [
                {
                    "id": row[0],
                    "name": row[1],
                    "description": row[2],
                    "image_url": row[3],
                    "expiration_time": datetime.fromisoformat(row[4]) if row[4] else None,
                    "created_at": datetime.fromisoformat(row[5])
                }
                for row in rows
            ]
    
    async def cleanup_expired_items(self):
        """Remove all expired items from the database."""
        current_time = datetime.now()
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                DELETE FROM inventory_items
                WHERE expiration_time < ?
            """, (current_time,))
            await db.commit()
            return cursor.rowcount

    async def delete_item(self, item_id: int):
        """Delete a specific item by ID."""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                cursor = await db.execute(
                    "DELETE FROM inventory_items WHERE id = ?",
                    (item_id,)
                )
                await db.commit()
                return cursor.rowcount > 0  # Return True if item was deleted
        except Exception as e:
            print(f"Error deleting item {item_id}: {e}")
            return False

# ================================
# DISCORD UI VIEWS
# ================================

class AddItemView(discord.ui.View):
    """Interactive view for adding items to inventory."""

    def __init__(self, bot, target_user: discord.Member, invoker: discord.Member):
        super().__init__(timeout=config.view_timeout)
        self.bot = bot
        self.target_user = target_user
        self.invoker = invoker

        # State tracking
        self.item_name: Optional[str] = None
        self.description: Optional[str] = None
        self.image_url: Optional[str] = None
        self.expiration_minutes: Optional[int] = None

        # Track which inputs are complete
        self.name_complete = False
        self.description_complete = False
        self.image_complete = False
        self.expiration_complete = False

        # Update button states
        self.update_button_states()

    def update_button_states(self):
        """Update button colors based on completion status."""
        # Update name button
        if self.name_complete:
            self.name_button.style = discord.ButtonStyle.success
            self.name_button.label = "✓ Name Added"
        else:
            self.name_button.style = discord.ButtonStyle.danger
            self.name_button.label = "Add Name"

        # Update description button
        if self.description_complete:
            self.description_button.style = discord.ButtonStyle.success
            self.description_button.label = "✓ Description Added"
        else:
            self.description_button.style = discord.ButtonStyle.danger
            self.description_button.label = "Add Description"

        # Update image button
        if self.image_complete:
            self.image_button.style = discord.ButtonStyle.success
            self.image_button.label = "✓ Image Added"
        else:
            self.image_button.style = discord.ButtonStyle.danger
            self.image_button.label = "Add Image"

        # Update expiration button
        if self.expiration_complete:
            self.expiration_button.style = discord.ButtonStyle.success
            self.expiration_button.label = "✓ Expiration Set"
        else:
            self.expiration_button.style = discord.ButtonStyle.danger
            self.expiration_button.label = "Add Expiration"

        # Update finish button
        all_complete = self.name_complete and self.description_complete and self.image_complete and self.expiration_complete
        self.finish_button.disabled = not all_complete
        if all_complete:
            self.finish_button.style = discord.ButtonStyle.primary
        else:
            self.finish_button.style = discord.ButtonStyle.secondary

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Ensure only the invoker can use the buttons."""
        if interaction.user != self.invoker:
            await interaction.response.send_message(
                "Only the person who invoked this command can use these buttons.",
                ephemeral=True
            )
            return False
        return True

    @discord.ui.button(label="Add Name", style=discord.ButtonStyle.danger, row=0)
    async def name_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle item name input."""
        timeout_minutes = config.input_wait_timeout // 60
        await interaction.response.send_message(
            f"Please provide a name for the item (you have {timeout_minutes} minutes, max 100 characters):",
            ephemeral=True
        )

        def check(message):
            return message.author == interaction.user and message.channel == interaction.channel

        try:
            message = await self.bot.wait_for('message', check=check, timeout=config.input_wait_timeout)

            # Validate name length
            if len(message.content) > 100:
                await interaction.followup.send(
                    "❌ Name too long! Maximum 100 characters allowed.",
                    ephemeral=True
                )
                return

            if len(message.content.strip()) == 0:
                await interaction.followup.send(
                    "❌ Name cannot be empty!",
                    ephemeral=True
                )
                return

            self.item_name = message.content.strip()
            self.name_complete = True

            # Delete the user's message for cleanliness
            try:
                await message.delete()
            except:
                pass

            self.update_button_states()

            # Update the original message
            embed = self.create_status_embed()
            await interaction.edit_original_response(embed=embed, view=self)

            await interaction.followup.send("✅ Name saved!", ephemeral=True)

        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)

    @discord.ui.button(label="Add Description", style=discord.ButtonStyle.danger, row=0)
    async def description_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle description input."""
        timeout_minutes = config.input_wait_timeout // 60
        await interaction.response.send_message(
            f"Please provide a description for the item (you have {timeout_minutes} minutes, max {config.max_description_length} characters):",
            ephemeral=True
        )

        def check(message):
            return message.author == interaction.user and message.channel == interaction.channel

        try:
            message = await self.bot.wait_for('message', check=check, timeout=config.input_wait_timeout)

            # Validate description length
            if len(message.content) > config.max_description_length:
                await interaction.followup.send(
                    f"❌ Description too long! Maximum {config.max_description_length} characters allowed.",
                    ephemeral=True
                )
                return

            self.description = message.content
            self.description_complete = True

            # Delete the user's message for cleanliness
            try:
                await message.delete()
            except:
                pass

            self.update_button_states()

            # Update the original message
            embed = self.create_status_embed()
            await interaction.edit_original_response(embed=embed, view=self)

            await interaction.followup.send("✅ Description saved!", ephemeral=True)

        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)

    async def compress_image(self, image_data: bytes, target_size: tuple) -> bytes:
        """Compress image to target size if it's larger."""
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                # Convert to RGB if necessary (for PNG with transparency)
                if img.mode in ('RGBA', 'LA', 'P'):
                    # Create a white background
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background
                elif img.mode != 'RGB':
                    img = img.convert('RGB')

                # If image is larger than target, resize it
                if img.size[0] > target_size[0] or img.size[1] > target_size[1]:
                    img.thumbnail(target_size, Image.Resampling.LANCZOS)

                    # If after thumbnail it's not exactly the target size, resize to exact size
                    if img.size != target_size:
                        img = img.resize(target_size, Image.Resampling.LANCZOS)

                # If image is smaller than target, resize to exact size
                elif img.size != target_size:
                    img = img.resize(target_size, Image.Resampling.LANCZOS)

                # Save compressed image
                output = io.BytesIO()
                img.save(output, format='JPEG', quality=85, optimize=True)
                return output.getvalue()
        except Exception:
            return image_data  # Return original if compression fails

    @discord.ui.button(label="Add Image", style=discord.ButtonStyle.danger, row=0)
    async def image_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle image input."""
        size = config.required_image_size
        timeout_minutes = config.input_wait_timeout // 60
        await interaction.response.send_message(
            f"Please upload an image (will be resized to {size[0]}x{size[1]} pixels if needed, you have {timeout_minutes} minutes):",
            ephemeral=True
        )

        def check(message):
            return (message.author == interaction.user and
                   message.channel == interaction.channel and
                   len(message.attachments) > 0)

        try:
            message = await self.bot.wait_for('message', check=check, timeout=config.input_wait_timeout)
            attachment = message.attachments[0]

            # Validate image
            if not attachment.content_type or not attachment.content_type.startswith('image/'):
                await interaction.followup.send("❌ Please upload a valid image file.", ephemeral=True)
                return

            # Check if we need to process the image
            needs_processing = False
            try:
                image_data = await attachment.read()
                with Image.open(io.BytesIO(image_data)) as img:
                    required_size = config.required_image_size
                    if img.size != required_size:
                        needs_processing = True
            except Exception:
                needs_processing = True  # Process if we can't determine size

            if needs_processing:
                # Process and resize image
                try:
                    processed_image_data = await self.compress_image(image_data, config.required_image_size)

                    # Create a new file with processed image
                    processed_file = discord.File(
                        io.BytesIO(processed_image_data),
                        filename=f"item_image_{interaction.user.id}_{int(datetime.now().timestamp())}.jpg"
                    )

                    # Send image to channel with a clear label
                    image_message = await interaction.channel.send(
                        content=f"�️ **Item Image for {self.target_user.display_name}** (Added by {interaction.user.display_name})",
                        file=processed_file
                    )
                    self.image_url = image_message.attachments[0].url

                    await interaction.followup.send("✅ Image processed and saved!", ephemeral=True)

                except Exception as e:
                    await interaction.followup.send(f"❌ Error processing image: {str(e)}", ephemeral=True)
                    return
            else:
                # Use original image URL if it's already the right size
                self.image_url = attachment.url
                await interaction.followup.send("✅ Image saved!", ephemeral=True)

            self.image_complete = True

            # Don't delete the user's message if we're using its attachment URL
            if needs_processing:
                try:
                    await message.delete()
                except:
                    pass

            self.update_button_states()

            # Update the original message
            embed = self.create_status_embed()
            await interaction.edit_original_response(embed=embed, view=self)

        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)

    @discord.ui.button(label="Add Expiration", style=discord.ButtonStyle.danger, row=0)
    async def expiration_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle expiration time input."""
        timeout_minutes = config.input_wait_timeout // 60
        max_days = config.max_expiration_days
        min_minutes = config.min_expiration_minutes
        await interaction.response.send_message(
            f"Please provide expiration time in minutes ({min_minutes}-{max_days*24*60} minutes, you have {timeout_minutes} minutes):",
            ephemeral=True
        )

        def check(message):
            return message.author == interaction.user and message.channel == interaction.channel

        try:
            message = await self.bot.wait_for('message', check=check, timeout=config.input_wait_timeout)

            try:
                minutes = int(message.content)
                if minutes < config.min_expiration_minutes:
                    await interaction.followup.send(f"❌ Minimum expiration time is {config.min_expiration_minutes} minutes.", ephemeral=True)
                    return

                max_minutes = config.max_expiration_days * 24 * 60
                if minutes > max_minutes:
                    await interaction.followup.send(f"❌ Maximum expiration time is {max_minutes} minutes ({config.max_expiration_days} days).", ephemeral=True)
                    return

                self.expiration_minutes = minutes
                self.expiration_complete = True

                # Delete the user's message for cleanliness
                try:
                    await message.delete()
                except:
                    pass

                self.update_button_states()

                # Update the original message
                embed = self.create_status_embed()
                await interaction.edit_original_response(embed=embed, view=self)

                await interaction.followup.send(f"✅ Expiration set to {minutes} minutes!", ephemeral=True)

            except ValueError:
                await interaction.followup.send("❌ Please provide a valid number.", ephemeral=True)

        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)

    @discord.ui.button(label="Finish Item", style=discord.ButtonStyle.secondary, disabled=True, row=1)
    async def finish_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Finish creating the item and add it to the database."""
        try:
            # Add item to database
            await self.bot.db.add_item(
                user_id=self.target_user.id,
                name=self.item_name,
                description=self.description,
                image_url=self.image_url,
                expiration_minutes=self.expiration_minutes
            )

            # Create success embed for the original interaction (private)
            private_embed = discord.Embed(
                title="✅ Item Added Successfully!",
                description=f"Item has been added to {self.target_user.mention}'s inventory.",
                color=int(config.embed_colors['success'].replace('#', ''), 16)
            )
            private_embed.add_field(name="Name", value=self.item_name, inline=True)
            private_embed.add_field(name="Description", value=self.description, inline=False)
            private_embed.add_field(name="Expires in", value=f"{self.expiration_minutes} minutes", inline=True)
            private_embed.set_thumbnail(url=self.image_url)

            # Disable all buttons
            for item in self.children:
                item.disabled = True

            # Update the original interaction message
            await interaction.response.edit_message(embed=private_embed, view=self)

            # Send a public announcement about the new item
            public_embed = discord.Embed(
                title="🎁 New Item Added!",
                description=f"**{self.item_name}** has been added to {self.target_user.mention}'s inventory by {self.invoker.mention}!",
                color=int(config.embed_colors['success'].replace('#', ''), 16)
            )
            public_embed.add_field(name="Item Name", value=self.item_name, inline=True)
            public_embed.add_field(name="Description", value=self.description, inline=False)

            # Calculate expiration time
            expiration_time = datetime.now() + timedelta(minutes=self.expiration_minutes)
            public_embed.add_field(
                name="Expires",
                value=f"<t:{int(expiration_time.timestamp())}:R>",
                inline=True
            )
            public_embed.set_thumbnail(url=self.image_url)
            public_embed.set_footer(text=f"Added by {self.invoker.display_name}")

            # Send public message to the channel
            await interaction.followup.send(embed=public_embed)

        except Exception as e:
            await interaction.response.send_message(f"❌ Error adding item: {str(e)}", ephemeral=True)

    def create_status_embed(self) -> discord.Embed:
        """Create an embed showing the current status."""
        embed = discord.Embed(
            title=f"Adding Item to {self.target_user.display_name}'s Inventory",
            description="Click the buttons below to add the required information:",
            color=int(config.embed_colors['pending'].replace('#', ''), 16)
        )

        # Add status fields
        name_status = "✅ Complete" if self.name_complete else "❌ Pending"
        desc_status = "✅ Complete" if self.description_complete else "❌ Pending"
        img_status = "✅ Complete" if self.image_complete else "❌ Pending"
        exp_status = "✅ Complete" if self.expiration_complete else "❌ Pending"

        size = config.required_image_size
        embed.add_field(name="Name", value=name_status, inline=True)
        embed.add_field(name="Description", value=desc_status, inline=True)
        embed.add_field(name=f"Image ({size[0]}x{size[1]})", value=img_status, inline=True)
        embed.add_field(name="Expiration Time", value=exp_status, inline=True)

        if self.image_url:
            embed.set_thumbnail(url=self.image_url)

        return embed

    async def on_timeout(self):
        """Handle view timeout."""
        for item in self.children:
            item.disabled = True

        embed = discord.Embed(
            title="⏰ Session Expired",
            description="The add item session has timed out. Please run the command again.",
            color=int(config.embed_colors['error'].replace('#', ''), 16)
        )

        try:
            # This might fail if the original interaction is too old
            await self.message.edit(embed=embed, view=self)
        except:
            pass

# ================================
# INVENTORY VIEW FOR LIVE UPDATES
# ================================

class InventoryView(discord.ui.View):
    """View for inventory display with refresh functionality."""

    def __init__(self, bot, target_user: discord.Member):
        super().__init__(timeout=300)  # 5 minute timeout
        self.bot = bot
        self.target_user = target_user

    @discord.ui.button(label="🔄 Refresh", style=discord.ButtonStyle.secondary)
    async def refresh_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Refresh the inventory display."""
        try:
            items = await self.bot.db.get_user_inventory(self.target_user.id)

            if not items:
                embed = discord.Embed(
                    title=f"{self.target_user.display_name}'s Inventory",
                    description="No items found in inventory.",
                    color=int(config.embed_colors['info'].replace('#', ''), 16)
                )
                await interaction.response.edit_message(embed=embed, view=self)
                return

            # Create embed with inventory items
            embed = discord.Embed(
                title=f"{self.target_user.display_name}'s Inventory",
                description=f"Found {len(items)} item(s)",
                color=int(config.embed_colors['success'].replace('#', ''), 16)
            )

            # Same logic as in inventory_command
            if len(items) <= 5:
                # Detailed view for 5 or fewer items
                for i, item in enumerate(items):
                    expiration_str = ""
                    if item['expiration_time']:
                        time_left = item['expiration_time'] - datetime.now()
                        if time_left.total_seconds() > 0:
                            total_seconds = int(time_left.total_seconds())
                            days = total_seconds // 86400
                            hours = (total_seconds % 86400) // 3600
                            minutes = (total_seconds % 3600) // 60
                            seconds = total_seconds % 60

                            if days > 0:
                                expiration_str = f"\n⏰ Expires in: {days}d {hours}h {minutes}m {seconds}s"
                            elif hours > 0:
                                expiration_str = f"\n⏰ Expires in: {hours}h {minutes}m {seconds}s"
                            else:
                                expiration_str = f"\n⏰ Expires in: {minutes}m {seconds}s"

                    # Add image URL after expiration time if available
                    image_str = ""
                    if item['image_url']:
                        image_str = f"\n🖼️ [View Image]({item['image_url']})"

                    embed.add_field(
                        name=f"**Item Name:** {item['name']}",
                        value=f"**Description:** {item['description']}{expiration_str}{image_str}",
                        inline=False
                    )

                    # Set full-size image for first item
                    if item['image_url'] and i == 0:
                        embed.set_image(url=item['image_url'])
            else:
                # Compact view for more than 5 items
                items_text = ""
                for i, item in enumerate(items[:15], 1):
                    expiration_str = ""
                    if item['expiration_time']:
                        time_left = item['expiration_time'] - datetime.now()
                        if time_left.total_seconds() > 0:
                            total_seconds = int(time_left.total_seconds())
                            days = total_seconds // 86400
                            hours = (total_seconds % 86400) // 3600
                            minutes = (total_seconds % 3600) // 60

                            if days > 0:
                                expiration_str = f" (⏰ {days}d {hours}h {minutes}m)"
                            elif hours > 0:
                                expiration_str = f" (⏰ {hours}h {minutes}m)"
                            else:
                                expiration_str = f" (⏰ {minutes}m)"

                    # Add image link after expiration time
                    image_str = ""
                    if item['image_url']:
                        image_str = f" [🖼️]({item['image_url']})"

                    items_text += f"{i}. **{item['name']}**{expiration_str}{image_str}\n"

                embed.add_field(
                    name="Items",
                    value=items_text,
                    inline=False
                )

                # Set full-size image from first item
                if items[0]['image_url']:
                    embed.set_image(url=items[0]['image_url'])

            await interaction.response.edit_message(embed=embed, view=self)

        except Exception as e:
            await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

    async def on_timeout(self):
        """Disable buttons when view times out."""
        for item in self.children:
            item.disabled = True

        try:
            await self.message.edit(view=self)
        except:
            pass

# ================================
# LOGGING SETUP
# ================================

def setup_logging():
    """Set up logging configuration."""
    if not config.logging_enabled:
        return

    # Create logger
    logger = logging.getLogger('discord')
    logger.setLevel(getattr(logging, config.log_level.upper(), logging.INFO))

    # Create file handler with rotation
    handler = RotatingFileHandler(
        config.log_file,
        maxBytes=config.log_max_file_size_mb * 1152 * 768,
        backupCount=config.log_backup_count
    )

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(handler)

    # Also log to console
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

# ================================
# PERMISSION HELPER
# ================================

def has_shop_keeper_permission(interaction: discord.Interaction) -> bool:
    """Check if user has permission to use shop keeper commands."""
    if not config.permission_checks_enabled:
        return True

    # Check for administrator permission
    if config.allow_administrator and interaction.user.guild_permissions.administrator:
        return True

    # Check for manage_guild permission
    if config.allow_manage_guild and interaction.user.guild_permissions.manage_guild:
        return True

    # Check for specific role (by name or ID)
    if hasattr(interaction.user, 'roles'):
        shop_keeper_role = config.shop_keeper_role.strip()
        for role in interaction.user.roles:
            # Check by role name (case insensitive)
            if role.name.lower() == shop_keeper_role.lower():
                return True
            # Check by role ID (convert both to string for comparison)
            if str(role.id) == shop_keeper_role:
                return True

    return False

# ================================
# MAIN BOT CLASS
# ================================

class InventoryBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        super().__init__(
            command_prefix=config.command_prefix,
            intents=intents,
            description=config.bot_description
        )
        self.db = InventoryDatabase(config.database_path)

    async def setup_hook(self):
        """Called when the bot is starting up."""
        await self.db.initialize()
        # Start the cleanup task if enabled
        if config.auto_cleanup_enabled:
            self.cleanup_expired_items.start()
        print(f"Bot is ready! Logged in as {self.user}")

    async def on_ready(self):
        """Called when the bot is ready."""
        try:
            synced = await self.tree.sync()
            print(f"Synced {len(synced)} command(s)")
        except Exception as e:
            print(f"Failed to sync commands: {e}")

    @tasks.loop(minutes=config.cleanup_interval_minutes)
    async def cleanup_expired_items(self):
        """Periodically clean up expired items."""
        try:
            deleted_count = await self.db.cleanup_expired_items()
            if deleted_count > 0:
                print(f"Cleaned up {deleted_count} expired items")
        except Exception as e:
            print(f"Error during cleanup: {e}")

# Create bot instance
bot = InventoryBot()

# ================================
# SLASH COMMANDS
# ================================

@bot.tree.command(name="inventory", description="View your inventory or another user's inventory")
@app_commands.describe(user="The user whose inventory you want to view (optional)")
async def inventory_command(interaction: discord.Interaction, user: Optional[discord.Member] = None):
    """Display inventory for the specified user or the command invoker."""
    target_user = user or interaction.user

    try:
        items = await bot.db.get_user_inventory(target_user.id)

        if not items:
            embed = discord.Embed(
                title=f"{target_user.display_name}'s Inventory",
                description="No items found in inventory.",
                color=int(config.embed_colors['info'].replace('#', ''), 16)
            )
            await interaction.response.send_message(embed=embed)
            return

        # Create embed with inventory items
        embed = discord.Embed(
            title=f"{target_user.display_name}'s Inventory",
            description=f"Found {len(items)} item(s)",
            color=int(config.embed_colors['success'].replace('#', ''), 16)
        )

        # Create multiple embeds if there are many items, or one detailed embed for fewer items
        if len(items) <= 5:
            # Detailed view for 5 or fewer items
            for i, item in enumerate(items):
                expiration_str = ""
                if item['expiration_time']:
                    time_left = item['expiration_time'] - datetime.now()
                    if time_left.total_seconds() > 0:
                        total_seconds = int(time_left.total_seconds())
                        days = total_seconds // 86400
                        hours = (total_seconds % 86400) // 3600
                        minutes = (total_seconds % 3600) // 60
                        seconds = total_seconds % 60

                        if days > 0:
                            expiration_str = f"\n⏰ Expires in: {days}d {hours}h {minutes}m {seconds}s"
                        elif hours > 0:
                            expiration_str = f"\n⏰ Expires in: {hours}h {minutes}m {seconds}s"
                        else:
                            expiration_str = f"\n⏰ Expires in: {minutes}m {seconds}s"

                # Add image URL after expiration time if available
                image_str = ""
                if item['image_url']:
                    image_str = f"\n🖼️ [View Image]({item['image_url']})"

                embed.add_field(
                    name=f"{item['name']}",
                    value=f"{item['description']}{expiration_str}{image_str}",
                    inline=False
                )

                # Set the first item's image as full-size main image (not thumbnail)
                if item['image_url'] and i == 0:
                    embed.set_image(url=item['image_url'])
        else:
            # Compact view for more than 5 items
            items_text = ""
            for i, item in enumerate(items[:15], 1):  # Show up to 15 items
                expiration_str = ""
                if item['expiration_time']:
                    time_left = item['expiration_time'] - datetime.now()
                    if time_left.total_seconds() > 0:
                        total_seconds = int(time_left.total_seconds())
                        days = total_seconds // 86400
                        hours = (total_seconds % 86400) // 3600
                        minutes = (total_seconds % 3600) // 60

                        if days > 0:
                            expiration_str = f" (⏰ {days}d {hours}h {minutes}m)"
                        elif hours > 0:
                            expiration_str = f" (⏰ {hours}h {minutes}m)"
                        else:
                            expiration_str = f" (⏰ {minutes}m)"

                # Add image link after expiration time
                image_str = ""
                if item['image_url']:
                    image_str = f" [🖼️]({item['image_url']})"

                items_text += f"{i}. **{item['name']}**{expiration_str}{image_str}\n"

            embed.add_field(
                name="Items",
                value=items_text,
                inline=False
            )

            # Set full-size image from first item
            if items[0]['image_url']:
                embed.set_image(url=items[0]['image_url'])

        # Create a view with refresh button for live updates
        view = InventoryView(bot, target_user)
        await interaction.response.send_message(embed=embed, view=view)

    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

@bot.tree.command(name="delete_item", description="Delete an item from a user's inventory (Shop Keeper only)")
@app_commands.describe(
    user="The user whose item to delete",
    item_name="The exact name of the item to delete"
)
async def delete_item_command(interaction: discord.Interaction, user: discord.Member, item_name: str):
    """Delete an item from a user's inventory."""
    # Check permissions
    if not has_shop_keeper_permission(interaction):
        await interaction.response.send_message(
            "❌ You don't have permission to use this command. You need the Shop Keeper role, Manage Server permission, or Administrator permission.",
            ephemeral=True
        )
        return

    try:
        # Get user's inventory to find the item
        items = await bot.db.get_user_inventory(user.id)

        if not items:
            await interaction.response.send_message(
                f"❌ {user.display_name} has no items in their inventory.",
                ephemeral=True
            )
            return

        # Find the item by name (case-insensitive)
        target_item = None
        for item in items:
            if item['name'].lower() == item_name.lower():
                target_item = item
                break

        if not target_item:
            # Show available items for reference
            available_items = [item['name'] for item in items[:10]]  # Show first 10 items
            items_list = "\n".join([f"• {name}" for name in available_items])
            if len(items) > 10:
                items_list += f"\n... and {len(items) - 10} more items"

            await interaction.response.send_message(
                f"❌ Item '{item_name}' not found in {user.display_name}'s inventory.\n\n**Available items:**\n{items_list}",
                ephemeral=True
            )
            return

        # Delete the item from database
        await bot.db.delete_item(target_item['id'])

        # Create success embed
        embed = discord.Embed(
            title="🗑️ Item Deleted Successfully",
            description=f"Item has been removed from {user.mention}'s inventory.",
            color=int(config.embed_colors['success'].replace('#', ''), 16)
        )
        embed.add_field(name="**Item Name:**", value=target_item['name'], inline=True)
        embed.add_field(name="**Description:**", value=target_item['description'], inline=False)
        embed.add_field(name="**Deleted by:**", value=interaction.user.mention, inline=True)

        if target_item['image_url']:
            embed.set_thumbnail(url=target_item['image_url'])

        embed.set_footer(text=f"Item ID: {target_item['id']}")

        # Send public message about deletion
        await interaction.response.send_message(embed=embed)

        # Also send a private confirmation
        await interaction.followup.send(
            f"✅ Successfully deleted '{target_item['name']}' from {user.display_name}'s inventory.",
            ephemeral=True
        )

    except Exception as e:
        await interaction.response.send_message(f"❌ An error occurred: {str(e)}", ephemeral=True)

@bot.tree.command(name="add_item", description="Add an item to a user's inventory (Shop Keeper only)")
@app_commands.describe(user="The user to add the item to")
async def add_item_command(interaction: discord.Interaction, user: discord.Member):
    """Add an item to a user's inventory with interactive workflow."""

    # Check permissions
    if not has_shop_keeper_permission(interaction):
        await interaction.response.send_message(
            f"❌ You don't have permission to use this command. You need the '{config.shop_keeper_role}' role or appropriate permissions.",
            ephemeral=True
        )
        return

    # Create the interactive view
    view = AddItemView(bot, user, interaction.user)
    embed = view.create_status_embed()

    await interaction.response.send_message(embed=embed, view=view)

    # Store the message reference for timeout handling
    view.message = await interaction.original_response()

@bot.tree.command(name="check_permissions", description="Check your permissions for shop keeper commands")
async def check_permissions_command(interaction: discord.Interaction):
    """Debug command to check user permissions."""
    user = interaction.user

    embed = discord.Embed(
        title="🔍 Permission Check",
        description=f"Checking permissions for {user.mention}",
        color=int(config.embed_colors['info'].replace('#', ''), 16)
    )

    # Check administrator
    is_admin = user.guild_permissions.administrator if hasattr(user, 'guild_permissions') else False
    embed.add_field(
        name="Administrator",
        value="✅ Yes" if is_admin else "❌ No",
        inline=True
    )

    # Check manage guild
    has_manage_guild = user.guild_permissions.manage_guild if hasattr(user, 'guild_permissions') else False
    embed.add_field(
        name="Manage Guild",
        value="✅ Yes" if has_manage_guild else "❌ No",
        inline=True
    )

    # Check roles
    user_roles = []
    target_role_found = False
    if hasattr(user, 'roles'):
        shop_keeper_role = config.shop_keeper_role.strip()
        for role in user.roles:
            if role.name != "@everyone":  # Skip @everyone role
                role_info = f"{role.name} (ID: {role.id})"
                user_roles.append(role_info)

                # Check if this role matches the shop keeper role
                if (role.name.lower() == shop_keeper_role.lower() or
                    str(role.id) == shop_keeper_role):
                    target_role_found = True

    embed.add_field(
        name=f"Target Role: {config.shop_keeper_role}",
        value="✅ Found" if target_role_found else "❌ Not Found",
        inline=True
    )

    # Show all user roles
    roles_text = "\n".join(user_roles) if user_roles else "No roles found"
    if len(roles_text) > 2048:  # Discord field limit
        roles_text = roles_text[:1021] + "..."

    embed.add_field(
        name="Your Roles",
        value=roles_text,
        inline=False
    )

    # Final permission result
    has_permission = has_shop_keeper_permission(interaction)
    embed.add_field(
        name="Can Use add_item Command",
        value="✅ Yes" if has_permission else "❌ No",
        inline=False
    )

    embed.set_footer(text="Use this to debug permission issues")

    await interaction.response.send_message(embed=embed, ephemeral=True)

@bot.tree.command(name="debug_images", description="Debug image display issues in inventory")
@app_commands.describe(user="The user whose inventory to debug (optional)")
async def debug_images_command(interaction: discord.Interaction, user: Optional[discord.Member] = None):
    """Debug command to check image URLs in inventory."""
    target_user = user or interaction.user

    try:
        items = await bot.db.get_user_inventory(target_user.id)

        embed = discord.Embed(
            title=f"🔍 Image Debug for {target_user.display_name}",
            description=f"Found {len(items)} items",
            color=int(config.embed_colors['info'].replace('#', ''), 16)
        )

        if not items:
            embed.add_field(name="No Items", value="No items found in inventory", inline=False)
        else:
            for i, item in enumerate(items[:5], 1):  # Show first 5 items
                image_status = "✅ Has URL" if item['image_url'] else "❌ No URL"

                # Test if URL is accessible
                url_test = "🔗 URL looks valid" if item['image_url'] and item['image_url'].startswith('http') else "⚠️ Invalid URL format"

                embed.add_field(
                    name=f"Item {i}: {item['name']}",
                    value=f"**Status:** {image_status}\n**URL Test:** {url_test}\n**URL:** {item['image_url'][:100] if item['image_url'] else 'None'}{'...' if item['image_url'] and len(item['image_url']) > 100 else ''}",
                    inline=False
                )

        # Add troubleshooting tips
        embed.add_field(
            name="🛠️ Troubleshooting Tips",
            value="• Images must be uploaded through the bot's interface\n• Don't delete messages containing item images\n• Images are automatically resized to 1152x768\n• Use /inventory and click 🔄 Refresh to update display",
            inline=False
        )

        await interaction.response.send_message(embed=embed, ephemeral=True)

    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

# ================================
# MAIN EXECUTION
# ================================

def validate_config():
    """Validate configuration and return list of errors."""
    errors = []

    token = config.token
    if not token or token == 'YOUR_BOT_TOKEN_HERE':
        errors.append("Discord bot token is not configured. Please set it in the CONFIG section at the top of this file.")

    if config.required_image_size[0] <= 0 or config.required_image_size[1] <= 0:
        errors.append("Invalid image size configuration")

    if config.max_items_per_user <= 0:
        errors.append("Max items per user must be positive")

    if config.cleanup_interval_minutes <= 0:
        errors.append("Cleanup interval must be positive")

    return errors

def main():
    """Main function to start the bot."""
    print("🤖 Discord Inventory Bot - All-in-One Version")
    print("=" * 50)

    # Validate configuration
    errors = validate_config()
    if errors:
        print("❌ Configuration errors found:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease fix the configuration in the CONFIG section and try again.")
        return

    print("✅ Configuration validated successfully!")

    # Setup logging
    setup_logging()

    print("🚀 Starting bot...")
    print(f"📊 Max items per user: {config.max_items_per_user}")
    print(f"🖼️  Required image size: {config.required_image_size[0]}x{config.required_image_size[1]}")
    print(f"👮 Shop keeper role: {config.shop_keeper_role}")
    print(f"🧹 Auto cleanup: {'Enabled' if config.auto_cleanup_enabled else 'Disabled'}")
    print(f"🔍 Image validation: {'Enabled' if config.image_validation_enabled else 'Disabled'}")
    print()
    print("💡 Tip: Use /check_permissions to debug role permission issues")
    print("💡 Tip: Make sure shop_keeper_role is set to exact role ID or name")
    print()

    try:
        # Run the bot
        bot.run(config.token)
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user.")
    except Exception as e:
        print(f"\n💥 Bot crashed: {e}")

if __name__ == "__main__":
    main()
