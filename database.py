import aiosqlite
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import os

class InventoryDatabase:
    def __init__(self, db_path: str = "inventory.db"):
        self.db_path = db_path
    
    async def initialize(self):
        """Initialize the database and create tables if they don't exist."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS inventory_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    description TEXT NOT NULL,
                    image_url TEXT,
                    expiration_time TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            await db.commit()
    
    async def add_item(self, user_id: int, description: str, image_url: str, expiration_minutes: int) -> int:
        """Add a new item to the inventory."""
        expiration_time = datetime.now() + timedelta(minutes=expiration_minutes)
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                INSERT INTO inventory_items (user_id, description, image_url, expiration_time)
                VALUES (?, ?, ?, ?)
            """, (user_id, description, image_url, expiration_time))
            await db.commit()
            return cursor.lastrowid
    
    async def get_user_inventory(self, user_id: int) -> List[Dict]:
        """Get all non-expired items for a user."""
        current_time = datetime.now()
        
        async with aiosqlite.connect(self.db_path) as db:
            # First, clean up expired items
            await db.execute("""
                DELETE FROM inventory_items 
                WHERE expiration_time < ?
            """, (current_time,))
            await db.commit()
            
            # Then get the remaining items
            cursor = await db.execute("""
                SELECT id, description, image_url, expiration_time, created_at
                FROM inventory_items
                WHERE user_id = ? AND expiration_time > ?
                ORDER BY created_at DESC
            """, (user_id, current_time))
            
            rows = await cursor.fetchall()
            return [
                {
                    "id": row[0],
                    "description": row[1],
                    "image_url": row[2],
                    "expiration_time": datetime.fromisoformat(row[3]) if row[3] else None,
                    "created_at": datetime.fromisoformat(row[4])
                }
                for row in rows
            ]
    
    async def cleanup_expired_items(self):
        """Remove all expired items from the database."""
        current_time = datetime.now()
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                DELETE FROM inventory_items 
                WHERE expiration_time < ?
            """, (current_time,))
            await db.commit()
            return cursor.rowcount
