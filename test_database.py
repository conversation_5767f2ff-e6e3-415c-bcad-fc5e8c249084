#!/usr/bin/env python3
"""
Test script for the inventory database functionality.
Run this to verify database operations work correctly.
"""

import asyncio
import os
from datetime import datetime, timedelta
from database import InventoryDatabase

async def test_database():
    """Test all database operations."""
    print("🧪 Testing Database Operations")
    print("=" * 40)
    
    # Use a test database
    test_db_path = "test_inventory.db"
    db = InventoryDatabase(test_db_path)
    
    try:
        # Initialize database
        print("1. Initializing database...")
        await db.initialize()
        print("✅ Database initialized")
        
        # Test adding items
        print("\n2. Adding test items...")
        
        # Add item that expires in 1 minute
        item1_id = await db.add_item(
            user_id=12345,
            description="Test Sword",
            image_url="https://example.com/sword.png",
            expiration_minutes=1
        )
        print(f"✅ Added item 1 (ID: {item1_id})")
        
        # Add item that expires in 60 minutes
        item2_id = await db.add_item(
            user_id=12345,
            description="Test Shield",
            image_url="https://example.com/shield.png",
            expiration_minutes=60
        )
        print(f"✅ Added item 2 (ID: {item2_id})")
        
        # Add item for different user
        item3_id = await db.add_item(
            user_id=67890,
            description="Test Potion",
            image_url="https://example.com/potion.png",
            expiration_minutes=30
        )
        print(f"✅ Added item 3 (ID: {item3_id})")
        
        # Test getting inventory
        print("\n3. Testing inventory retrieval...")
        
        inventory1 = await db.get_user_inventory(12345)
        print(f"✅ User 12345 has {len(inventory1)} items")
        for item in inventory1:
            time_left = item['expiration_time'] - datetime.now()
            minutes_left = int(time_left.total_seconds() / 60)
            print(f"   - {item['description']} (expires in {minutes_left} minutes)")
        
        inventory2 = await db.get_user_inventory(67890)
        print(f"✅ User 67890 has {len(inventory2)} items")
        for item in inventory2:
            time_left = item['expiration_time'] - datetime.now()
            minutes_left = int(time_left.total_seconds() / 60)
            print(f"   - {item['description']} (expires in {minutes_left} minutes)")
        
        # Test cleanup (simulate expired items)
        print("\n4. Testing cleanup of expired items...")
        print("⏳ Waiting 65 seconds to test expiration...")
        
        # For testing purposes, let's manually expire the first item
        import aiosqlite
        async with aiosqlite.connect(test_db_path) as conn:
            # Set first item to expire 1 minute ago
            past_time = datetime.now() - timedelta(minutes=1)
            await conn.execute(
                "UPDATE inventory_items SET expiration_time = ? WHERE id = ?",
                (past_time, item1_id)
            )
            await conn.commit()
        
        # Now test cleanup
        deleted_count = await db.cleanup_expired_items()
        print(f"✅ Cleaned up {deleted_count} expired items")
        
        # Check inventory again
        inventory1_after = await db.get_user_inventory(12345)
        print(f"✅ User 12345 now has {len(inventory1_after)} items (should be 1 less)")
        
        print("\n🎉 All database tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
    
    finally:
        # Clean up test database
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
            print(f"🧹 Cleaned up test database: {test_db_path}")

async def main():
    """Run the database tests."""
    await test_database()

if __name__ == "__main__":
    asyncio.run(main())
