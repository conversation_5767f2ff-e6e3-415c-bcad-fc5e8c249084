import discord
from discord.ext import commands, tasks
from discord import app_commands
import os
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
import io
from PIL import Image
import logging
from logging.handlers import RotatingFileHandler

from database import InventoryDatabase
from views import AddItemView
from config import config

def setup_logging():
    """Set up logging configuration."""
    if not config.logging_enabled:
        return

    # Create logger
    logger = logging.getLogger('discord')
    logger.setLevel(getattr(logging, config.log_level.upper(), logging.INFO))

    # Create file handler with rotation
    handler = RotatingFileHandler(
        config.log_file,
        maxBytes=config.log_max_file_size_mb * 1024 * 1024,
        backupCount=config.log_backup_count
    )

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(handler)

    # Also log to console
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

class InventoryBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        super().__init__(
            command_prefix=config.command_prefix,
            intents=intents,
            description=config.bot_description
        )
        self.db = InventoryDatabase(config.database_path)
        
    async def setup_hook(self):
        """Called when the bot is starting up."""
        await self.db.initialize()
        # Start the cleanup task if enabled
        if config.auto_cleanup_enabled:
            self.cleanup_expired_items.start()
        print(f"Bot is ready! Logged in as {self.user}")
    
    async def on_ready(self):
        """Called when the bot is ready."""
        try:
            synced = await self.tree.sync()
            print(f"Synced {len(synced)} command(s)")
        except Exception as e:
            print(f"Failed to sync commands: {e}")
    
    @tasks.loop(minutes=config.cleanup_interval_minutes)
    async def cleanup_expired_items(self):
        """Periodically clean up expired items."""
        try:
            deleted_count = await self.db.cleanup_expired_items()
            if deleted_count > 0:
                print(f"Cleaned up {deleted_count} expired items")
        except Exception as e:
            print(f"Error during cleanup: {e}")

# Create bot instance
bot = InventoryBot()

def has_shop_keeper_permission(interaction: discord.Interaction) -> bool:
    """Check if user has permission to use shop keeper commands."""
    if not config.permission_checks_enabled:
        return True

    # Check for administrator permission
    if config.allow_administrator and interaction.user.guild_permissions.administrator:
        return True

    # Check for manage_guild permission
    if config.allow_manage_guild and interaction.user.guild_permissions.manage_guild:
        return True

    # Check for specific role
    if hasattr(interaction.user, 'roles'):
        for role in interaction.user.roles:
            if role.name == config.shop_keeper_role:
                return True

    return False

@bot.tree.command(name="inventory", description="View your inventory or another user's inventory")
@app_commands.describe(user="The user whose inventory you want to view (optional)")
async def inventory_command(interaction: discord.Interaction, user: Optional[discord.Member] = None):
    """Display inventory for the specified user or the command invoker."""
    target_user = user or interaction.user
    
    try:
        items = await bot.db.get_user_inventory(target_user.id)
        
        if not items:
            embed = discord.Embed(
                title=f"{target_user.display_name}'s Inventory",
                description="No items found in inventory.",
                color=int(config.embed_colors['info'].replace('#', ''), 16)
            )
            await interaction.response.send_message(embed=embed)
            return
        
        # Create embed with inventory items
        embed = discord.Embed(
            title=f"{target_user.display_name}'s Inventory",
            description=f"Found {len(items)} item(s)",
            color=int(config.embed_colors['success'].replace('#', ''), 16)
        )
        
        for i, item in enumerate(items[:10]):  # Limit to 10 items per page
            expiration_str = ""
            if item['expiration_time']:
                time_left = item['expiration_time'] - datetime.now()
                if time_left.total_seconds() > 0:
                    hours = int(time_left.total_seconds() // 3600)
                    minutes = int((time_left.total_seconds() % 3600) // 60)
                    expiration_str = f"\n⏰ Expires in: {hours}h {minutes}m"
            
            embed.add_field(
                name=f"Item #{i+1}",
                value=f"{item['description']}{expiration_str}",
                inline=False
            )
        
        # Set thumbnail if first item has an image
        if items and items[0]['image_url']:
            embed.set_thumbnail(url=items[0]['image_url'])
        
        await interaction.response.send_message(embed=embed)
        
    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

@bot.tree.command(name="add_item", description="Add an item to a user's inventory (Shop Keeper only)")
@app_commands.describe(user="The user to add the item to")
async def add_item_command(interaction: discord.Interaction, user: discord.Member):
    """Add an item to a user's inventory with interactive workflow."""

    # Check permissions
    if not has_shop_keeper_permission(interaction):
        await interaction.response.send_message(
            f"❌ You don't have permission to use this command. You need the '{config.shop_keeper_role}' role or appropriate permissions.",
            ephemeral=True
        )
        return

    # Create the interactive view
    view = AddItemView(bot, user, interaction.user)
    embed = view.create_status_embed()

    await interaction.response.send_message(embed=embed, view=view)

    # Store the message reference for timeout handling
    view.message = await interaction.original_response()

if __name__ == "__main__":
    # Validate configuration
    errors = config.validate_config()
    if errors:
        print("❌ Configuration errors found:")
        for error in errors:
            print(f"  - {error}")
        print("\nPlease fix the configuration and try again.")
        exit(1)

    # Setup logging
    setup_logging()

    # Run the bot
    bot.run(config.token)
