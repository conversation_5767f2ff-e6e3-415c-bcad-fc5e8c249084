import discord
from discord.ext import commands, tasks
from discord import app_commands
import os
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
import io
from PIL import Image
from dotenv import load_dotenv

from database import InventoryDatabase
from views import AddItemView

# Load environment variables
load_dotenv()

# Bot configuration
TOKEN = os.getenv('DISCORD_TOKEN')
SHOP_KEEPER_ROLE = os.getenv('SHOP_KEEPER_ROLE', 'Shop Keeper')
DATABASE_PATH = os.getenv('DATABASE_PATH', 'inventory.db')

class InventoryBot(commands.Bot):
    def __init__(self):
        intents = discord.Intents.default()
        intents.message_content = True
        super().__init__(command_prefix='!', intents=intents)
        self.db = InventoryDatabase(DATABASE_PATH)
        
    async def setup_hook(self):
        """Called when the bot is starting up."""
        await self.db.initialize()
        # Start the cleanup task
        self.cleanup_expired_items.start()
        print(f"Bo<PERSON> is ready! Logged in as {self.user}")
    
    async def on_ready(self):
        """Called when the bot is ready."""
        try:
            synced = await self.tree.sync()
            print(f"Synced {len(synced)} command(s)")
        except Exception as e:
            print(f"Failed to sync commands: {e}")
    
    @tasks.loop(minutes=30)
    async def cleanup_expired_items(self):
        """Periodically clean up expired items."""
        try:
            deleted_count = await self.db.cleanup_expired_items()
            if deleted_count > 0:
                print(f"Cleaned up {deleted_count} expired items")
        except Exception as e:
            print(f"Error during cleanup: {e}")

# Create bot instance
bot = InventoryBot()

def has_shop_keeper_permission(interaction: discord.Interaction) -> bool:
    """Check if user has permission to use shop keeper commands."""
    # Check for manage_guild permission
    if interaction.user.guild_permissions.manage_guild:
        return True
    
    # Check for specific role
    if hasattr(interaction.user, 'roles'):
        for role in interaction.user.roles:
            if role.name == SHOP_KEEPER_ROLE:
                return True
    
    return False

@bot.tree.command(name="inventory", description="View your inventory or another user's inventory")
@app_commands.describe(user="The user whose inventory you want to view (optional)")
async def inventory_command(interaction: discord.Interaction, user: Optional[discord.Member] = None):
    """Display inventory for the specified user or the command invoker."""
    target_user = user or interaction.user
    
    try:
        items = await bot.db.get_user_inventory(target_user.id)
        
        if not items:
            embed = discord.Embed(
                title=f"{target_user.display_name}'s Inventory",
                description="No items found in inventory.",
                color=discord.Color.blue()
            )
            await interaction.response.send_message(embed=embed)
            return
        
        # Create embed with inventory items
        embed = discord.Embed(
            title=f"{target_user.display_name}'s Inventory",
            description=f"Found {len(items)} item(s)",
            color=discord.Color.green()
        )
        
        for i, item in enumerate(items[:10]):  # Limit to 10 items per page
            expiration_str = ""
            if item['expiration_time']:
                time_left = item['expiration_time'] - datetime.now()
                if time_left.total_seconds() > 0:
                    hours = int(time_left.total_seconds() // 3600)
                    minutes = int((time_left.total_seconds() % 3600) // 60)
                    expiration_str = f"\n⏰ Expires in: {hours}h {minutes}m"
            
            embed.add_field(
                name=f"Item #{i+1}",
                value=f"{item['description']}{expiration_str}",
                inline=False
            )
        
        # Set thumbnail if first item has an image
        if items and items[0]['image_url']:
            embed.set_thumbnail(url=items[0]['image_url'])
        
        await interaction.response.send_message(embed=embed)
        
    except Exception as e:
        await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

@bot.tree.command(name="add_item", description="Add an item to a user's inventory (Shop Keeper only)")
@app_commands.describe(user="The user to add the item to")
async def add_item_command(interaction: discord.Interaction, user: discord.Member):
    """Add an item to a user's inventory with interactive workflow."""

    # Check permissions
    if not has_shop_keeper_permission(interaction):
        await interaction.response.send_message(
            "❌ You don't have permission to use this command. You need the 'Shop Keeper' role or 'Manage Guild' permission.",
            ephemeral=True
        )
        return

    # Create the interactive view
    view = AddItemView(bot, user, interaction.user)
    embed = view.create_status_embed()

    await interaction.response.send_message(embed=embed, view=view)

    # Store the message reference for timeout handling
    view.message = await interaction.original_response()

if __name__ == "__main__":
    if not TOKEN:
        print("Error: DISCORD_TOKEN not found in environment variables!")
        print("Please create a .env file based on .env.example")
        exit(1)
    
    bot.run(TOKEN)
