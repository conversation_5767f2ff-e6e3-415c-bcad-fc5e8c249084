import discord
from discord.ext import commands
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import io
from PIL import Image

class AddItemView(discord.ui.View):
    def __init__(self, bot, target_user: discord.Member, invoker: discord.Member):
        super().__init__(timeout=300)  # 5 minute timeout
        self.bot = bot
        self.target_user = target_user
        self.invoker = invoker
        
        # State tracking
        self.description: Optional[str] = None
        self.image_url: Optional[str] = None
        self.expiration_minutes: Optional[int] = None
        
        # Track which inputs are complete
        self.description_complete = False
        self.image_complete = False
        self.expiration_complete = False
        
        # Update button states
        self.update_button_states()
    
    def update_button_states(self):
        """Update button colors based on completion status."""
        # Update description button
        if self.description_complete:
            self.description_button.style = discord.ButtonStyle.success
            self.description_button.label = "✓ Description Added"
        else:
            self.description_button.style = discord.ButtonStyle.danger
            self.description_button.label = "Add Description"
        
        # Update image button
        if self.image_complete:
            self.image_button.style = discord.ButtonStyle.success
            self.image_button.label = "✓ Image Added"
        else:
            self.image_button.style = discord.ButtonStyle.danger
            self.image_button.label = "Add Image"
        
        # Update expiration button
        if self.expiration_complete:
            self.expiration_button.style = discord.ButtonStyle.success
            self.expiration_button.label = "✓ Expiration Set"
        else:
            self.expiration_button.style = discord.ButtonStyle.danger
            self.expiration_button.label = "Add Expiration"
        
        # Update finish button
        all_complete = self.description_complete and self.image_complete and self.expiration_complete
        self.finish_button.disabled = not all_complete
        if all_complete:
            self.finish_button.style = discord.ButtonStyle.primary
        else:
            self.finish_button.style = discord.ButtonStyle.secondary
    
    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        """Ensure only the invoker can use the buttons."""
        if interaction.user != self.invoker:
            await interaction.response.send_message(
                "Only the person who invoked this command can use these buttons.", 
                ephemeral=True
            )
            return False
        return True
    
    @discord.ui.button(label="Add Description", style=discord.ButtonStyle.danger, row=0)
    async def description_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle description input."""
        await interaction.response.send_message(
            "Please provide a description for the item (you have 2 minutes):", 
            ephemeral=True
        )
        
        def check(message):
            return message.author == interaction.user and message.channel == interaction.channel
        
        try:
            message = await self.bot.wait_for('message', check=check, timeout=120)
            self.description = message.content
            self.description_complete = True
            
            # Delete the user's message for cleanliness
            try:
                await message.delete()
            except:
                pass
            
            self.update_button_states()
            
            # Update the original message
            embed = self.create_status_embed()
            await interaction.edit_original_response(embed=embed, view=self)
            
            await interaction.followup.send("✅ Description saved!", ephemeral=True)
            
        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)
    
    @discord.ui.button(label="Add Image", style=discord.ButtonStyle.danger, row=0)
    async def image_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle image input."""
        await interaction.response.send_message(
            "Please upload an image (must be exactly 128x128 pixels, you have 2 minutes):", 
            ephemeral=True
        )
        
        def check(message):
            return (message.author == interaction.user and 
                   message.channel == interaction.channel and 
                   len(message.attachments) > 0)
        
        try:
            message = await self.bot.wait_for('message', check=check, timeout=120)
            attachment = message.attachments[0]
            
            # Validate image
            if not attachment.content_type or not attachment.content_type.startswith('image/'):
                await interaction.followup.send("❌ Please upload a valid image file.", ephemeral=True)
                return
            
            # Download and validate dimensions
            image_data = await attachment.read()
            try:
                with Image.open(io.BytesIO(image_data)) as img:
                    if img.size != (128, 128):
                        await interaction.followup.send(
                            f"❌ Image must be exactly 128x128 pixels. Your image is {img.size[0]}x{img.size[1]}.", 
                            ephemeral=True
                        )
                        return
            except Exception as e:
                await interaction.followup.send("❌ Invalid image file.", ephemeral=True)
                return
            
            self.image_url = attachment.url
            self.image_complete = True
            
            # Delete the user's message for cleanliness
            try:
                await message.delete()
            except:
                pass
            
            self.update_button_states()
            
            # Update the original message
            embed = self.create_status_embed()
            await interaction.edit_original_response(embed=embed, view=self)
            
            await interaction.followup.send("✅ Image saved!", ephemeral=True)
            
        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)
    
    @discord.ui.button(label="Add Expiration", style=discord.ButtonStyle.danger, row=0)
    async def expiration_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle expiration time input."""
        await interaction.response.send_message(
            "Please provide expiration time in minutes (positive integer, you have 2 minutes):", 
            ephemeral=True
        )
        
        def check(message):
            return message.author == interaction.user and message.channel == interaction.channel
        
        try:
            message = await self.bot.wait_for('message', check=check, timeout=120)
            
            try:
                minutes = int(message.content)
                if minutes <= 0:
                    await interaction.followup.send("❌ Please provide a positive number of minutes.", ephemeral=True)
                    return
                
                self.expiration_minutes = minutes
                self.expiration_complete = True
                
                # Delete the user's message for cleanliness
                try:
                    await message.delete()
                except:
                    pass
                
                self.update_button_states()
                
                # Update the original message
                embed = self.create_status_embed()
                await interaction.edit_original_response(embed=embed, view=self)
                
                await interaction.followup.send(f"✅ Expiration set to {minutes} minutes!", ephemeral=True)
                
            except ValueError:
                await interaction.followup.send("❌ Please provide a valid number.", ephemeral=True)
                
        except asyncio.TimeoutError:
            await interaction.followup.send("⏰ Timeout! Please try again.", ephemeral=True)
    
    @discord.ui.button(label="Finish Item", style=discord.ButtonStyle.secondary, disabled=True, row=1)
    async def finish_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Finish creating the item and add it to the database."""
        try:
            # Add item to database
            item_id = await self.bot.db.add_item(
                user_id=self.target_user.id,
                description=self.description,
                image_url=self.image_url,
                expiration_minutes=self.expiration_minutes
            )
            
            # Create success embed
            embed = discord.Embed(
                title="✅ Item Added Successfully!",
                description=f"Item has been added to {self.target_user.mention}'s inventory.",
                color=discord.Color.green()
            )
            embed.add_field(name="Description", value=self.description, inline=False)
            embed.add_field(name="Expires in", value=f"{self.expiration_minutes} minutes", inline=True)
            embed.set_thumbnail(url=self.image_url)
            
            # Disable all buttons
            for item in self.children:
                item.disabled = True
            
            await interaction.response.edit_message(embed=embed, view=self)
            
        except Exception as e:
            await interaction.response.send_message(f"❌ Error adding item: {str(e)}", ephemeral=True)
    
    def create_status_embed(self) -> discord.Embed:
        """Create an embed showing the current status."""
        embed = discord.Embed(
            title=f"Adding Item to {self.target_user.display_name}'s Inventory",
            description="Click the buttons below to add the required information:",
            color=discord.Color.orange()
        )
        
        # Add status fields
        desc_status = "✅ Complete" if self.description_complete else "❌ Pending"
        img_status = "✅ Complete" if self.image_complete else "❌ Pending"
        exp_status = "✅ Complete" if self.expiration_complete else "❌ Pending"
        
        embed.add_field(name="Description", value=desc_status, inline=True)
        embed.add_field(name="Image (128x128)", value=img_status, inline=True)
        embed.add_field(name="Expiration Time", value=exp_status, inline=True)
        
        if self.image_url:
            embed.set_thumbnail(url=self.image_url)
        
        return embed
    
    async def on_timeout(self):
        """Handle view timeout."""
        for item in self.children:
            item.disabled = True
        
        embed = discord.Embed(
            title="⏰ Session Expired",
            description="The add item session has timed out. Please run the command again.",
            color=discord.Color.red()
        )
        
        try:
            # This might fail if the original interaction is too old
            await self.message.edit(embed=embed, view=self)
        except:
            pass
