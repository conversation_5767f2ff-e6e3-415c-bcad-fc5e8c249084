#!/usr/bin/env python3
"""
Discord Inventory Bot Launcher
Simple script to run the bot with proper error handling and setup validation.
"""

import os
import sys
from pathlib import Path

def check_requirements():
    """Check if all requirements are met before starting the bot."""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        return False
    
    # Check if .env file exists
    if not Path('.env').exists():
        print("❌ .env file not found!")
        print("📝 Please copy .env.example to .env and configure your bot token.")
        return False
    
    # Check if required packages are installed
    try:
        import discord
        import aiosqlite
        from PIL import Image
        from dotenv import load_dotenv
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("📦 Please run: pip install -r requirements.txt")
        return False
    
    # Check if bot token is configured
    from dotenv import load_dotenv
    load_dotenv()
    
    token = os.getenv('DISCORD_TOKEN')
    if not token or token == 'your_bot_token_here':
        print("❌ Discord bot token not configured!")
        print("🔑 Please set DISCORD_TOKEN in your .env file.")
        return False
    
    print("✅ All requirements met!")
    return True

def main():
    """Main function to start the bot."""
    print("🤖 Discord Inventory Bot")
    print("=" * 30)
    
    if not check_requirements():
        print("\n❌ Setup incomplete. Please fix the issues above and try again.")
        sys.exit(1)
    
    print("🚀 Starting bot...")
    
    try:
        # Import and run the bot
        from bot import bot, TOKEN
        bot.run(TOKEN)
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user.")
    except Exception as e:
        print(f"\n💥 Bot crashed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
