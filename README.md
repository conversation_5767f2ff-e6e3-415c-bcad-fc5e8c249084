# Discord Inventory Bot

A Discord bot that manages user inventories with expiring items. Features slash commands for viewing inventories and adding items with an interactive workflow.

## Features

- **Inventory Management**: Users can view their own or others' inventories
- **Item Expiration**: Items automatically expire and are cleaned up
- **Interactive Item Creation**: Shop keepers can add items through an interactive button interface
- **Image Validation**: Ensures uploaded images are exactly 128x128 pixels
- **Permission System**: Restricts item creation to users with appropriate permissions
- **SQLite Database**: Persistent storage with automatic cleanup

## Commands

### `/inventory [user]`
- **Access**: Anyone
- **Description**: Display inventory for yourself or another user
- **Parameters**: 
  - `user` (optional): The user whose inventory to view

### `/add_item <user>`
- **Access**: Users with "Shop Keeper" role or "Manage Guild" permission
- **Description**: Add an item to a user's inventory through interactive workflow
- **Parameters**:
  - `user` (required): The user to add the item to

## Setup Instructions

### 1. Prerequisites
- Python 3.8 or higher
- Discord <PERSON><PERSON> Token (from Discord Developer Portal)

### 2. Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure the bot (choose one method):

   **Option A: JSON Configuration (Recommended)**
   ```bash
   cp config.json.example config.json
   ```
   Edit `config.json` with your settings, especially the bot token.

   **Option B: Environment Variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` file with your bot token:
   ```
   DISCORD_TOKEN=your_bot_token_here
   SHOP_KEEPER_ROLE=Shop Keeper
   DATABASE_PATH=inventory.db
   ```

   **Note**: Environment variables take priority over JSON config values.

### 3. Discord Bot Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to "Bot" section and create a bot
4. Copy the bot token to your `.env` file
5. Under "Privileged Gateway Intents", enable:
   - Message Content Intent (for reading messages during item creation)

### 4. Bot Permissions

When inviting the bot to your server, ensure it has these permissions:
- Send Messages
- Use Slash Commands
- Embed Links
- Attach Files
- Read Message History
- Manage Messages (for cleaning up user input messages)

### 5. Running the Bot

```bash
python bot.py
```

The bot will:
- Initialize the SQLite database
- Sync slash commands with Discord
- Start the periodic cleanup task for expired items

## Usage

### Adding Items (Shop Keepers)

1. Use `/add_item @user` command
2. Click the three red buttons to add:
   - **Description**: Text description of the item
   - **Image**: Upload a 128x128 pixel image
   - **Expiration**: Time in minutes until the item expires
3. Once all three are complete (buttons turn green), click "Finish Item"
4. The item is added to the user's inventory

### Viewing Inventories

1. Use `/inventory` to view your own inventory
2. Use `/inventory @user` to view another user's inventory
3. Expired items are automatically filtered out

## Technical Details

### Database Schema

The bot uses SQLite with the following table:

```sql
CREATE TABLE inventory_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    image_url TEXT,
    expiration_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### File Structure

- `bot.py` - Main bot file with commands and setup
- `database.py` - Database operations and models
- `views.py` - Discord UI components (buttons, interactions)
- `config.py` - Configuration management system
- `requirements.txt` - Python dependencies
- `config.json.example` - JSON configuration template
- `.env.example` - Environment variables template
- `run.py` - Bot launcher with validation
- `test_database.py` - Database testing script
- `README.md` - This documentation

### Configuration Options

The bot supports extensive configuration through `config.json`:

- **Bot Settings**: Token, prefix, description
- **Database**: Path, cleanup intervals
- **Permissions**: Role names, permission levels
- **Inventory Limits**: Max items, description length, expiration limits
- **UI Customization**: Colors, timeouts
- **Feature Toggles**: Enable/disable validation, cleanup, etc.
- **Logging**: Levels, file rotation

See `config.json.example` for all available options.

### Security Features

- Permission checks for restricted commands
- User interaction validation (only command invoker can use buttons)
- Input timeouts (2 minutes per input)
- Image validation (size and format)
- Automatic cleanup of expired items

## Troubleshooting

### Common Issues

1. **Bot not responding to slash commands**
   - Ensure bot has "Use Slash Commands" permission
   - Check that commands are synced (bot logs this on startup)

2. **Permission denied for add_item**
   - User needs "Shop Keeper" role or "Manage Guild" permission
   - Check role name matches SHOP_KEEPER_ROLE in .env

3. **Image upload fails**
   - Image must be exactly 128x128 pixels
   - Must be a valid image format (PNG, JPG, etc.)

4. **Database errors**
   - Ensure bot has write permissions in the directory
   - Check DATABASE_PATH in .env file

### Logs

The bot logs important events to console:
- Command sync status
- Expired item cleanup
- Errors and exceptions

## License

This project is provided as-is for educational and personal use.
