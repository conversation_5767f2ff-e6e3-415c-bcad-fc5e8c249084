import json
import os
from typing import Dict, Any, Optional, List, Tuple
from dotenv import load_dotenv

class BotConfig:
    """Configuration manager that supports both JSON config and environment variables."""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self._config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self):
        """Load configuration from JSON file and environment variables."""
        # Load environment variables first
        load_dotenv()
        
        # Load JSON config if it exists
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self._config = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"Warning: Could not load {self.config_file}: {e}")
                self._config = {}
        else:
            print(f"Warning: {self.config_file} not found, using environment variables only")
            self._config = {}
    
    def get(self, key_path: str, default: Any = None, env_var: Optional[str] = None) -> Any:
        """
        Get a configuration value by key path (e.g., 'bot.token' or 'database.path').
        
        Priority order:
        1. Environment variable (if env_var is specified)
        2. JSON config value
        3. Default value
        """
        # Check environment variable first if specified
        if env_var:
            env_value = os.getenv(env_var)
            if env_value is not None:
                # Try to convert to appropriate type
                return self._convert_env_value(env_value, default)
        
        # Navigate through JSON config
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def _convert_env_value(self, value: str, default: Any) -> Any:
        """Convert environment variable string to appropriate type based on default."""
        if isinstance(default, bool):
            return value.lower() in ('true', '1', 'yes', 'on')
        elif isinstance(default, int):
            try:
                return int(value)
            except ValueError:
                return default
        elif isinstance(default, float):
            try:
                return float(value)
            except ValueError:
                return default
        elif isinstance(default, list):
            # For lists, expect comma-separated values
            return [item.strip() for item in value.split(',') if item.strip()]
        else:
            return value
    
    # Bot configuration properties
    @property
    def token(self) -> str:
        return self.get('bot.token', env_var='DISCORD_TOKEN')
    
    @property
    def command_prefix(self) -> str:
        return self.get('bot.command_prefix', '!', env_var='COMMAND_PREFIX')
    
    @property
    def bot_description(self) -> str:
        return self.get('bot.description', 'Discord Inventory Management Bot')
    
    # Database configuration
    @property
    def database_path(self) -> str:
        return self.get('database.path', 'inventory.db', env_var='DATABASE_PATH')
    
    @property
    def cleanup_interval_minutes(self) -> int:
        return self.get('database.cleanup_interval_minutes', 30, env_var='CLEANUP_INTERVAL_MINUTES')
    
    # Permission configuration
    @property
    def shop_keeper_role(self) -> str:
        return self.get('permissions.shop_keeper_role', 'Shop Keeper', env_var='SHOP_KEEPER_ROLE')
    
    @property
    def allow_manage_guild(self) -> bool:
        return self.get('permissions.allow_manage_guild', True, env_var='ALLOW_MANAGE_GUILD')
    
    @property
    def allow_administrator(self) -> bool:
        return self.get('permissions.allow_administrator', True, env_var='ALLOW_ADMINISTRATOR')
    
    # Inventory configuration
    @property
    def max_items_per_user(self) -> int:
        return self.get('inventory.max_items_per_user', 50, env_var='MAX_ITEMS_PER_USER')
    
    @property
    def max_description_length(self) -> int:
        return self.get('inventory.max_description_length', 500, env_var='MAX_DESCRIPTION_LENGTH')
    
    @property
    def required_image_size(self) -> Tuple[int, int]:
        size = self.get('inventory.required_image_size', [128, 128])
        return tuple(size) if isinstance(size, list) and len(size) == 2 else (128, 128)
    
    @property
    def max_expiration_days(self) -> int:
        return self.get('inventory.max_expiration_days', 365, env_var='MAX_EXPIRATION_DAYS')
    
    @property
    def min_expiration_minutes(self) -> int:
        return self.get('inventory.min_expiration_minutes', 1, env_var='MIN_EXPIRATION_MINUTES')
    
    # UI configuration
    @property
    def embed_colors(self) -> Dict[str, str]:
        return self.get('ui.embed_colors', {
            'success': '#00ff00',
            'error': '#ff0000',
            'warning': '#ffaa00',
            'info': '#0099ff',
            'pending': '#ff6600'
        })
    
    @property
    def button_interaction_timeout(self) -> int:
        return self.get('ui.timeouts.button_interaction_seconds', 300, env_var='BUTTON_TIMEOUT')
    
    @property
    def input_wait_timeout(self) -> int:
        return self.get('ui.timeouts.input_wait_seconds', 120, env_var='INPUT_TIMEOUT')
    
    @property
    def view_timeout(self) -> int:
        return self.get('ui.timeouts.view_timeout_seconds', 300, env_var='VIEW_TIMEOUT')
    
    # Feature flags
    @property
    def auto_cleanup_enabled(self) -> bool:
        return self.get('features.auto_cleanup_enabled', True, env_var='AUTO_CLEANUP_ENABLED')
    
    @property
    def image_validation_enabled(self) -> bool:
        return self.get('features.image_validation_enabled', True, env_var='IMAGE_VALIDATION_ENABLED')
    
    @property
    def permission_checks_enabled(self) -> bool:
        return self.get('features.permission_checks_enabled', True, env_var='PERMISSION_CHECKS_ENABLED')
    
    @property
    def logging_enabled(self) -> bool:
        return self.get('features.logging_enabled', True, env_var='LOGGING_ENABLED')
    
    # Logging configuration
    @property
    def log_level(self) -> str:
        return self.get('logging.level', 'INFO', env_var='LOG_LEVEL')
    
    @property
    def log_file(self) -> str:
        return self.get('logging.file', 'bot.log', env_var='LOG_FILE')
    
    @property
    def log_max_file_size_mb(self) -> int:
        return self.get('logging.max_file_size_mb', 10, env_var='LOG_MAX_FILE_SIZE_MB')
    
    @property
    def log_backup_count(self) -> int:
        return self.get('logging.backup_count', 3, env_var='LOG_BACKUP_COUNT')
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        if not self.token or self.token == 'your_bot_token_here':
            errors.append("Discord bot token is not configured")
        
        if self.required_image_size[0] <= 0 or self.required_image_size[1] <= 0:
            errors.append("Invalid image size configuration")
        
        if self.max_items_per_user <= 0:
            errors.append("Max items per user must be positive")
        
        if self.cleanup_interval_minutes <= 0:
            errors.append("Cleanup interval must be positive")
        
        return errors

# Global config instance
config = BotConfig()
